public abstract class BaseGridObject
{
    protected GridSystem<BaseGridObject> gridSystem;
    protected GridPosition gridPosition;

    public BaseGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition)
    {
        this.gridSystem = gridSystem;
        this.gridPosition = gridPosition;
    }

    public GridPosition GetGridPosition()
    {
        return gridPosition;
    }

    public abstract string GetDescription(); // Ph<PERSON>ơng thức trừu tượng để các lớp con triển khai
} 