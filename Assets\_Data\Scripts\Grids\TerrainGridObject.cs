public class TerrainGridObject : BaseGridObject
{
    public string terrainType; // <PERSON><PERSON><PERSON> địa hình (Grass, Water, Mountain, etc.)
    public float height; // Đ<PERSON> cao của ô

    public string resourceType; // <PERSON><PERSON><PERSON> tài nguyên (Gold, Wood, etc.)
    public int resourceAmount; // Số lượng tài nguyên

    public TerrainGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition, string terrainType, float height, string resourceType = null, int resourceAmount = 0)
        : base(gridSystem, gridPosition)
    {
        this.terrainType = terrainType;
        this.height = height;
        this.resourceType = resourceType;
        this.resourceAmount = resourceAmount;
    }

    public override string GetDescription()
    {
        string description = $"Terrain: {terrainType}, Height: {height}";
        if (!string.IsNullOrEmpty(resourceType))
        {
            description += $", Resource: {resourceType}, Amount: {resourceAmount}";
        }
        return description;
    }
} 