// 8/3/2025 AI-Tag
// This was created with the help of Assistant, a Unity Artificial Intelligence product.

Dựa trên mã nguồn bạn cung cấp, tôi sẽ hỗ trợ triển khai <b>Cách 2 (Tách riêng thành nhiều loại `GridObject`)</b>. Trong cách này, chúng ta sẽ tạo các lớp con kế thừa từ `BaseGridObject` để đại diện cho các loại đối tượng khác nhau, như `TerrainGridObject`, `ResourceGridObject`, và `BuildingGridObject`.

---

### <b>1. Cập nhật `GridObject` thành `BaseGridObject`</b>
Chúng ta sẽ biến `GridObject` hiện tại thành một lớp cơ sở (`BaseGridObject`) để các lớp con kế thừa.

#### Cập nhật `BaseGridObject`
```csharp filename="BaseGridObject.cs"
public abstract class BaseGridObject
{
    protected GridSystem<BaseGridObject> gridSystem;
    protected GridPosition gridPosition;

    public BaseGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition)
    {
        this.gridSystem = gridSystem;
        this.gridPosition = gridPosition;
    }

    public GridPosition GetGridPosition()
    {
        return gridPosition;
    }

    public abstract string GetDescription(); // Phương thức trừu tượng để các lớp con triển khai
}
```

---

### <b>2. Tạo các lớp con cho từng loại `GridObject`</b>

#### Lớp `TerrainGridObject`
Lớp này sẽ lưu trữ thông tin về địa hình, như loại địa hình và độ cao.

```csharp filename="TerrainGridObject.cs"
public class TerrainGridObject : BaseGridObject
{
    public string terrainType; // Loại địa hình (Grass, Water, Mountain, etc.)
    public float height; // Độ cao của ô

    public TerrainGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition, string terrainType, float height)
        : base(gridSystem, gridPosition)
    {
        this.terrainType = terrainType;
        this.height = height;
    }

    public override string GetDescription()
    {
        return $"Terrain: {terrainType}, Height: {height}";
    }
}
```

#### Lớp `ResourceGridObject`
Lớp này sẽ lưu trữ thông tin về tài nguyên, như loại tài nguyên và số lượng.

```csharp filename="ResourceGridObject.cs"
public class ResourceGridObject : BaseGridObject
{
    public string resourceType; // Loại tài nguyên (Gold, Wood, etc.)
    public int resourceAmount; // Số lượng tài nguyên

    public ResourceGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition, string resourceType, int resourceAmount)
        : base(gridSystem, gridPosition)
    {
        this.resourceType = resourceType;
        this.resourceAmount = resourceAmount;
    }

    public override string GetDescription()
    {
        return $"Resource: {resourceType}, Amount: {resourceAmount}";
    }
}
```

#### Lớp `BuildingGridObject`
Lớp này sẽ lưu trữ thông tin về trạng thái xây dựng, như loại công trình và trạng thái chiếm dụng.

```csharp filename="BuildingGridObject.cs"
public class BuildingGridObject : BaseGridObject
{
    public bool isOccupied; // Ô này có bị chiếm bởi công trình không
    public string buildingType; // Loại công trình (House, Factory, etc.)

    public BuildingGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition, string buildingType)
        : base(gridSystem, gridPosition)
    {
        this.isOccupied = true;
        this.buildingType = buildingType;
    }

    public override string GetDescription()
    {
        return $"Building: {buildingType}, Occupied: {isOccupied}";
    }
}
```

---

### <b>3. Cập nhật `LevelGrid` để hỗ trợ nhiều loại `GridObject`</b>
Chúng ta sẽ cập nhật `LevelGrid` để khởi tạo `GridSystem` với các loại `GridObject` khác nhau.

#### Cập nhật `LevelGrid`
```csharp filename="LevelGrid.cs"
using UnityEngine;

public class LevelGrid : MonoBehaviour
{
    [SerializeField] private int width = 10;
    [SerializeField] private int height = 10;
    [SerializeField] private float cellSize = 2f;

    public static LevelGrid Instance { get; private set; }

    private GridSystem<BaseGridObject> gridSystem;

    private void Awake()
    {
        Instance = this;

        // Khởi tạo GridSystem với TerrainGridObject mặc định
        gridSystem = new GridSystem<BaseGridObject>(width, height, cellSize, (GridSystem<BaseGridObject> g, GridPosition gridPosition) =>
        {
            // Mặc định tạo TerrainGridObject
            return new TerrainGridObject(g, gridPosition, "Grass", 0f);
        });
    }

    private void OnDrawGizmos()
    {
        if (gridSystem == null) return;

        for (int x = 0; x < gridSystem.GetWidth(); x++)
        {
            for (int z = 0; z < gridSystem.GetHeight(); z++)
            {
                GridPosition gridPosition = new GridPosition(x, z);
                Vector3 worldPosition = gridSystem.GetWorldPosition(gridPosition);
                Gizmos.color = Color.white;
                Gizmos.DrawWireCube(worldPosition + new Vector3(cellSize, 0, cellSize) * 0.5f, new Vector3(cellSize, 0.1f, cellSize));

                // Hiển thị thông tin GridObject
                BaseGridObject gridObject = gridSystem.GetGridObject(gridPosition);
                if (gridObject != null)
                {
                    Gizmos.color = Color.green;
                    Gizmos.DrawCube(worldPosition + Vector3.up * 0.1f, Vector3.one * cellSize * 0.5f);
                }
            }
        }
    }

    public void SetGridObject(GridPosition gridPosition, BaseGridObject gridObject)
    {
        gridSystem.SetGridObject(gridPosition, gridObject);
    }

    public BaseGridObject GetGridObject(GridPosition gridPosition)
    {
        return gridSystem.GetGridObject(gridPosition);
    }
}
```

---

### <b>4. Sử dụng hệ thống</b>
Bạn có thể thay đổi loại `GridObject` tại một vị trí cụ thể bằng cách sử dụng `SetGridObject`.

#### Ví dụ
```csharp filename="ExampleUsage.cs"
using UnityEngine;

public class ExampleUsage : MonoBehaviour
{
    private void Start()
    {
        // Đặt một BuildingGridObject tại vị trí (2, 3)
        GridPosition position = new GridPosition(2, 3);
        LevelGrid.Instance.SetGridObject(position, new BuildingGridObject(LevelGrid.Instance.GetGridObject(position).gridSystem, position, "House"));

        // Đặt một ResourceGridObject tại vị trí (5, 5)
        position = new GridPosition(5, 5);
        LevelGrid.Instance.SetGridObject(position, new ResourceGridObject(LevelGrid.Instance.GetGridObject(position).gridSystem, position, "Gold", 100));
    }
}
```

---

### <b>5. Kết quả</b>
- Mỗi ô trong Grid có thể chứa một loại `GridObject` cụ thể (`TerrainGridObject`, `ResourceGridObject`, hoặc `BuildingGridObject`).
- Bạn có thể dễ dàng mở rộng hệ thống bằng cách thêm các lớp con mới kế thừa từ `BaseGridObject`.
- Hệ thống `GridSystem` vẫn giữ nguyên, không cần thay đổi.

Bạn muốn tôi hỗ trợ thêm gì không, ví dụ như tích hợp hệ thống này với Unity Editor hoặc thêm tính năng đặc biệt cho từng loại `GridObject`?


