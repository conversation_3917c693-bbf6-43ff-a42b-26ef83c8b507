using System;
using System.Collections.Generic;
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;

// 8/3/2025 AI-Tag
// This was created with the help of <PERSON>, a Unity Artificial Intelligence product.
public class PathFinding : MonoBehaviour
{
    private const int MOVE_STRAIGHT_COST = 10;
    private const int MOVE_DIAGONAL_COST = 14;

    private GridSystem<PathGridObject> pathGridSystem;

    public void CreatePathGrid(int width, int height, float cellSize)
    {
        if (pathGridSystem != null && pathGridSystem.GetWidth() == width && pathGridSystem.GetHeight() == height)
        {
            // Grid already exists with the same dimensions, no need to recreate.
            return;
        }
        
        pathGridSystem = new GridSystem<PathGridObject>(width, height, cellSize, (GridSystem<PathGridObject> g, GridPosition gridPosition) =>
        {
            return new PathGridObject(g, gridPosition);
        });
    }

    public List<GridPosition> FindPath(GridPosition startGridPosition, GridPosition endGridPosition, bool allowDiagonal = true)
    {
        int width = pathGridSystem.GetWidth();
        int height = pathGridSystem.GetHeight();

        var pathNodeArray = new NativeArray<PathNode>(width * height, Allocator.TempJob);
        try
        {
            for (int x = 0; x < width; x++)
            {
                for (int z = 0; z < height; z++)
                {
                    var pathNode = new PathNode
                    {
                        x = x,
                        z = z,
                        index = CalculateIndex(x, z, width),
                        gCost = int.MaxValue,
                        fCost = int.MaxValue,
                        isWalkable = pathGridSystem.GetGridObject(new GridPosition(x, z)).IsWalkable(),
                        cameFromNodeIndex = -1
                    };
                    pathNodeArray[pathNode.index] = pathNode;
                }
            }

            using (var resultPath = new NativeList<GridPosition>(Allocator.TempJob))
            {
                var pathFindingJob = new PathFindingJob
                {
                    startPosition = startGridPosition,
                    endPosition = endGridPosition,
                    gridSize = new int2(width, height),
                    pathNodeArray = pathNodeArray,
                    path = resultPath,
                    allowDiagonal = allowDiagonal,
                };

                JobHandle jobHandle = pathFindingJob.Schedule();
                jobHandle.Complete();

                if (resultPath.Length == 0)
                {
                    Debug.LogWarning($"Path not found from {startGridPosition} to {endGridPosition}");
                    return null; // No path found
                }

                List<GridPosition> pathList = new List<GridPosition>(resultPath.AsArray());
                pathList.Reverse();
                return pathList;
            }
        }
        finally
        {
            pathNodeArray.Dispose();
        }
    }

    public List<GridPosition> FindPathNoDiagonal(GridPosition startGridPosition, GridPosition endGridPosition)
    {
        return FindPath(startGridPosition, endGridPosition, false);
    }

    public PathGridObject GetNode(int x, int z)
    {
        return pathGridSystem.GetGridObject(new GridPosition(x, z));
    }

    private int CalculateIndex(int x, int z, int gridWidth)
    {
        return x + z * gridWidth;
    }
}

[BurstCompile]
public struct PathFindingJob : IJob
{
    private const int MOVE_STRAIGHT_COST = 10;
    private const int MOVE_DIAGONAL_COST = 14;

    public GridPosition startPosition;
    public GridPosition endPosition;
    public int2 gridSize;
    public bool allowDiagonal;

    public NativeArray<PathNode> pathNodeArray;
    [WriteOnly] public NativeList<GridPosition> path;

    public void Execute()
    {
        int startNodeIndex = CalculateIndex(startPosition.x, startPosition.z, gridSize.x);
        int endNodeIndex = CalculateIndex(endPosition.x, endPosition.z, gridSize.x);

        PathNode startNode = pathNodeArray[startNodeIndex];
        startNode.gCost = 0;
        startNode.hCost = CalculateDistanceCost(startPosition, endPosition, allowDiagonal);
        startNode.fCost = startNode.gCost + startNode.hCost;
        pathNodeArray[startNodeIndex] = startNode;

        var openList = new NativePriorityQueue<int>(gridSize.x * gridSize.y, Allocator.Temp);
        var closedList = new NativeHashSet<int>(gridSize.x * gridSize.y, Allocator.Temp);
        
        openList.Enqueue(startNodeIndex, pathNodeArray);

        while (openList.Count > 0)
        {
            int currentNodeIndex = openList.Dequeue(pathNodeArray);
            PathNode currentNode = pathNodeArray[currentNodeIndex];

            if (currentNodeIndex == endNodeIndex)
            {
                CalculatePath(pathNodeArray, currentNode);
                break;
            }

            closedList.Add(currentNodeIndex);

            NativeList<int> neighbourList = GetNeighbourList(currentNode, gridSize, allowDiagonal);
            foreach (int neighbourIndex in neighbourList)
            {
                if (closedList.Contains(neighbourIndex)) continue;

                PathNode neighbourNode = pathNodeArray[neighbourIndex];
                if (!neighbourNode.isWalkable)
                {
                    closedList.Add(neighbourIndex);
                    continue;
                }
                
                GridPosition currentNodeGridPos = new GridPosition(currentNode.x, currentNode.z);
                GridPosition neighbourNodeGridPos = new GridPosition(neighbourNode.x, neighbourNode.z);
                
                int tentativeGCost = currentNode.gCost + CalculateDistanceCost(currentNodeGridPos, neighbourNodeGridPos, allowDiagonal);
                if (tentativeGCost < neighbourNode.gCost)
                {
                    neighbourNode.cameFromNodeIndex = currentNodeIndex;
                    neighbourNode.gCost = tentativeGCost;
                    neighbourNode.hCost = CalculateDistanceCost(neighbourNodeGridPos, endPosition, allowDiagonal);
                    neighbourNode.fCost = neighbourNode.gCost + neighbourNode.hCost;
                    pathNodeArray[neighbourIndex] = neighbourNode;

                    if (!openList.Contains(neighbourIndex))
                    {
                        openList.Enqueue(neighbourIndex, pathNodeArray);
                    }
                }
            }
            neighbourList.Dispose();
        }
        
        openList.Dispose();
        closedList.Dispose();
    }
    
    private void CalculatePath(NativeArray<PathNode> pathNodeArray, PathNode endNode)
    {
        if (endNode.cameFromNodeIndex == -1)
        {
            // No path
            return;
        }
        
        path.Add(new GridPosition(endNode.x, endNode.z));
        PathNode currentNode = endNode;
        while(currentNode.cameFromNodeIndex != -1)
        {
            PathNode cameFromNode = pathNodeArray[currentNode.cameFromNodeIndex];
            path.Add(new GridPosition(cameFromNode.x, cameFromNode.z));
            currentNode = cameFromNode;
        }
    }
    
    private int CalculateDistanceCost(GridPosition a, GridPosition b, bool allowDiagonal)
    {
        int xDistance = math.abs(a.x - b.x);
        int zDistance = math.abs(a.z - b.z);
        
        if (allowDiagonal)
        {
            int remaining = math.abs(xDistance - zDistance);
            return MOVE_DIAGONAL_COST * math.min(xDistance, zDistance) + MOVE_STRAIGHT_COST * remaining;
        }
        else
        {
            return MOVE_STRAIGHT_COST * (xDistance + zDistance);
        }
    }
    
    private NativeList<int> GetNeighbourList(PathNode currentNode, int2 gridSize, bool allowDiagonal)
    {
        NativeList<int> neighbourList = new NativeList<int>(8, Allocator.Temp);

        // Straight
        if (currentNode.x - 1 >= 0) neighbourList.Add(CalculateIndex(currentNode.x - 1, currentNode.z, gridSize.x));
        if (currentNode.x + 1 < gridSize.x) neighbourList.Add(CalculateIndex(currentNode.x + 1, currentNode.z, gridSize.x));
        if (currentNode.z - 1 >= 0) neighbourList.Add(CalculateIndex(currentNode.x, currentNode.z - 1, gridSize.x));
        if (currentNode.z + 1 < gridSize.y) neighbourList.Add(CalculateIndex(currentNode.x, currentNode.z + 1, gridSize.x));

        if (allowDiagonal)
        {
            // Diagonal
            if (currentNode.x - 1 >= 0 && currentNode.z - 1 >= 0) neighbourList.Add(CalculateIndex(currentNode.x - 1, currentNode.z - 1, gridSize.x));
            if (currentNode.x + 1 < gridSize.x && currentNode.z - 1 >= 0) neighbourList.Add(CalculateIndex(currentNode.x + 1, currentNode.z - 1, gridSize.x));
            if (currentNode.x - 1 >= 0 && currentNode.z + 1 < gridSize.y) neighbourList.Add(CalculateIndex(currentNode.x - 1, currentNode.z + 1, gridSize.x));
            if (currentNode.x + 1 < gridSize.x && currentNode.z + 1 < gridSize.y) neighbourList.Add(CalculateIndex(currentNode.x + 1, currentNode.z + 1, gridSize.x));
        }
        
        return neighbourList;
    }
    
    private int CalculateIndex(int x, int z, int gridWidth)
    {
        return x + z * gridWidth;
    }
}

public struct PathNode
{
    public int x;
    public int z;
    public int index;

    public int gCost;
    public int hCost;
    public int fCost;

    public bool isWalkable;
    public int cameFromNodeIndex;
}

// A Min-Heap Priority Queue implementation for Native collections
public struct NativePriorityQueue<T> : IDisposable where T : unmanaged, IEquatable<T>
{
    private NativeList<T> heap;
    private NativeHashMap<T, int> itemIndexMap;

    public NativePriorityQueue(int capacity, Allocator allocator)
    {
        heap = new NativeList<T>(capacity, allocator);
        itemIndexMap = new NativeHashMap<T, int>(capacity, allocator);
    }
    
    public int Count => heap.Length;

    public void Enqueue(T item, NativeArray<PathNode> pathNodeArray)
    {
        heap.Add(item);
        itemIndexMap[item] = heap.Length - 1;
        SiftUp(heap.Length - 1, pathNodeArray);
    }

    public T Dequeue(NativeArray<PathNode> pathNodeArray)
    {
        T item = heap[0];
        int lastIndex = heap.Length - 1;
        heap[0] = heap[lastIndex];
        itemIndexMap[heap[0]] = 0;
        heap.RemoveAt(lastIndex);
        itemIndexMap.Remove(item);

        if (heap.Length > 0)
        {
            SiftDown(0, pathNodeArray);
        }
        return item;
    }
    
    public bool Contains(T item)
    {
        return itemIndexMap.ContainsKey(item);
    }

    private void SiftUp(int index, NativeArray<PathNode> pathNodeArray)
    {
        T item = heap[index];
        int parentIndex = (index - 1) / 2;

        while (index > 0 && Compare(item, heap[parentIndex], pathNodeArray) < 0)
        {
            heap[index] = heap[parentIndex];
            itemIndexMap[heap[index]] = index;
            index = parentIndex;
            parentIndex = (index - 1) / 2;
        }
        heap[index] = item;
        itemIndexMap[item] = index;
    }

    private void SiftDown(int index, NativeArray<PathNode> pathNodeArray)
    {
        T item = heap[index];
        int size = heap.Length;
        
        while (index < size / 2)
        {
            int childIndex = index * 2 + 1;
            T child = heap[childIndex];
            int rightChildIndex = childIndex + 1;
            
            if (rightChildIndex < size && Compare(heap[rightChildIndex], child, pathNodeArray) < 0)
            {
                childIndex = rightChildIndex;
                child = heap[childIndex];
            }
            
            if (Compare(item, child, pathNodeArray) <= 0) break;
            
            heap[index] = child;
            itemIndexMap[child] = index;
            index = childIndex;
        }
        heap[index] = item;
        itemIndexMap[item] = index;
    }
    
    private int Compare(T a, T b, NativeArray<PathNode> pathNodeArray)
    {
        // This is a bit of a hack since we can't pass a comparer struct easily.
        // We assume T is an int (node index)
        int indexA = (int)(object)a;
        int indexB = (int)(object)b;
        return pathNodeArray[indexA].fCost.CompareTo(pathNodeArray[indexB].fCost);
    }

    public void Dispose()
    {
        heap.Dispose();
        itemIndexMap.Dispose();
    }
}
