public class BuildingGridObject : BaseGridObject
{
    public bool isOccupied; // Ô này có bị chiếm bởi công trình không
    public string buildingType; // Loại công trình (House, Factory, etc.)

    public BuildingGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition, string buildingType)
        : base(gridSystem, gridPosition)
    {
        this.isOccupied = true;
        this.buildingType = buildingType;
    }

    public override string GetDescription()
    {
        return $"Building: {buildingType}, Occupied: {isOccupied}";
    }
} 