using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PathGridObject : BaseGridObject
{
    private int gCost;
    private int hCost;
    private int fCost;
    private bool isWalkable;
    private PathGridObject cameFromNode;

    public PathGridObject(GridSystem<BaseGridObject> gridSystem, GridPosition gridPosition) : base(gridSystem, gridPosition)
    {
        isWalkable = true;
    }

    // Constructor for PathGridSystem
    public PathGridObject(GridSystem<PathGridObject> gridSystem, GridPosition gridPosition) : base(null, gridPosition)
    {
        // We pass null to the base constructor because this grid object belongs to the PathGridSystem,
        // not the main game world's grid system.
        isWalkable = true;
    }

    public override string GetDescription()
    {
        return gCost + "," + hCost;
    }

    public void CalculateFCost()
    {
        fCost = gCost + hCost;
    }

    public int GetFCost() { return fCost; }
    public int GetGCost() { return gCost; }
    public int GetHCost() { return hCost; }
    public void SetGCost(int gCost) { this.gCost = gCost; }
    public void SetHCost(int hCost) { this.hCost = hCost; }
    public PathGridObject GetCameFromNode() { return cameFromNode; }
    public void SetCameFromNode(PathGridObject cameFromNode) { this.cameFromNode = cameFromNode; }


    public bool IsWalkable()
    {
        return isWalkable;
    }

    public void SetIsWalkable(bool isWalkable)
    {
        this.isWalkable = isWalkable;
    }
} 