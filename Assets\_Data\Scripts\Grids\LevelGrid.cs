using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class LevelGrid : MonoBehaviour
{
    [Header("Grid Settings")]
    [SerializeField] private int width = 10;
    [SerializeField] private int height = 10;
    [SerializeField] private float cellSize = 2f;
    [Header("Perlin Noise")]
    [SerializeField] private float noiseScale = 0.1f;
    [SerializeField] private float seaLevelThreshold = 0.4f;
    [SerializeField] private AnimationCurve falloffCurve; // Thay thế cho islandFalloff

    [Header("Mesh Generation")]
    [SerializeField] private float heightMultiplier = 5f;
    [SerializeField] private float landHeightValue = 1f;
    [SerializeField] private float seaHeightValue = 0f;
    public Material terrainMaterial;
    public Material edgeMaterial;

    [Header("Tree Generation")]
    public GameObject[] treePrefabs;
    [SerializeField] private float treeNoiseScale = 0.05f;
    [SerializeField, Range(0f, 1f)] private float treeDensity = 0.5f;

    [Header("River Generation")]
    [SerializeField] private float riverNoiseScale = 0.05f;
    [SerializeField, Range(0f, 1f)] private float riverDensity = 0.4f;
    [SerializeField] private int riverCount = 10;

    [SerializeField] private float xOffset;
    [SerializeField] private float zOffset;

    [SerializeField] private PathFinding pathFinding;


    public static LevelGrid Instance { get; private set; }

    private GridSystem<BaseGridObject> gridSystem;
    
    private Mesh mesh;

    private void Start()
    {
        StartCoroutine(GenerateWorldCoroutine());
    }

    

    private IEnumerator GenerateWorldCoroutine()
    {
        Instance = this;

        xOffset = Random.Range(-9999f, 9999f);
        zOffset = Random.Range(-9999f, 9999f);

        float centerX = width / 2f;
        float centerZ = height / 2f;
        float maxDistance = Vector2.Distance(Vector2.zero, new Vector2(centerX, centerZ));

        gridSystem = new GridSystem<BaseGridObject>(width, height, cellSize, (GridSystem<BaseGridObject> g, GridPosition gridPosition) =>
        {
            float perlinValue = Mathf.PerlinNoise(
                (gridPosition.x + xOffset) * noiseScale,
                (gridPosition.z + zOffset) * noiseScale
            );

            float distanceToCenter = Vector2.Distance(new Vector2(gridPosition.x, gridPosition.z), new Vector2(centerX, centerZ));
            float normalizedDistance = distanceToCenter / maxDistance;
            float falloff = falloffCurve.Evaluate(normalizedDistance);

            float finalValue = perlinValue * falloff;

            string terrainType = finalValue > seaLevelThreshold ? "Land" : "Sea";
            float heightValue = (terrainType == "Land") ? landHeightValue : seaHeightValue;

            return new TerrainGridObject(g, gridPosition, terrainType, heightValue);
        });

        yield return GenerateRivers();

        mesh = new Mesh();
        GetComponent<MeshFilter>().mesh = mesh;
        
        GenerateMesh();
        DrawTexture();
        DrawEdgeMesh();
        GenerateTrees();
    }

    private IEnumerator GenerateRivers()
    {
        if (pathFinding == null)
        {
            Debug.LogError("PathFinding component is not assigned in the Inspector!");
            yield break;
        }

        pathFinding.CreatePathGrid(width, height, cellSize);

        List<GridPosition> walkableLandPositions = new List<GridPosition>();

        float riverXOffset = Random.Range(-9999f, 9999f);
        float riverZOffset = Random.Range(-9999f, 9999f);
        for (int x = 0; x < width; x++)
        {
            for (int z = 0; z < height; z++)
            {
                var pathNode = pathFinding.GetNode(x, z);
                pathNode.SetIsWalkable(false);

                float noiseValue = Mathf.PerlinNoise(
                        (x + riverXOffset) * riverNoiseScale,
                        (z + riverZOffset) * riverNoiseScale
                    );

                if (noiseValue > riverDensity)
                {
                    pathNode.SetIsWalkable(true);
                    walkableLandPositions.Add(new GridPosition(x, z));
                }
            }
        }

        if (walkableLandPositions.Count < 2)
        {
            Debug.LogWarning("Could not find at least two walkable land positions to create a river. Check island generation parameters and river density.");
            yield break;
        }

        int riversCreated = 0;
        for (int i = 0; i < riverCount; i++)
        {
            int startIndex = Random.Range(0, walkableLandPositions.Count);
            int endIndex;
            do
            {
                endIndex = Random.Range(0, walkableLandPositions.Count);
            } while (endIndex == startIndex);

            GridPosition startPosition = walkableLandPositions[startIndex];
            GridPosition endPosition = walkableLandPositions[endIndex];

            List<GridPosition> path = pathFinding.FindPathNoDiagonal(startPosition, endPosition);

            if (path != null)
            {
                riversCreated++;
                foreach (GridPosition gridPosition in path)
                {
                    var terrainObject = gridSystem.GetGridObject(gridPosition) as TerrainGridObject;
                    if (terrainObject != null)
                    {
                        terrainObject.terrainType = "Sea";
                        terrainObject.height = seaHeightValue;
                    }
                }
            }
            yield return null;
        }
        
        if (riversCreated == 0)
        {
            Debug.LogWarning("Failed to create any rivers. Pathfinding might be failing. Check river density and walkable areas.");
        }
        else
        {
            Debug.Log($"Successfully created {riversCreated} out of {riverCount} potential rivers.");
        }
    }



    private void GenerateTrees()
    {
        GameObject treeContainer = new GameObject("Trees");
        treeContainer.transform.parent = transform;
        treeContainer.transform.localPosition = Vector3.zero;

        // Sử dụng một offset khác cho noise của cây để không bị trùng với noise địa hình
        float treeXOffset = Random.Range(-9999f, 9999f);
        float treeZOffset = Random.Range(-9999f, 9999f);

        for (int x = 0; x < width; x++)
        {
            for (int z = 0; z < height; z++)
            {
                // Chỉ đặt cây trên đất liền
                if (GetTerrainTypeAt(x, z) == "Land")
                {
                    // Tạo noise cho cây
                    float treeNoiseValue = Mathf.PerlinNoise(
                        (x + treeXOffset) * treeNoiseScale,
                        (z + treeZOffset) * treeNoiseScale
                    );

                    // So sánh noise với density để quyết định đặt cây
                    if (treeNoiseValue < treeDensity)
                    {
                        // Lấy độ cao của ô đất
                        TerrainGridObject currentCell = gridSystem.GetGridObject(new GridPosition(x, z)) as TerrainGridObject;
                        float landY = currentCell.height * heightMultiplier;

                        // Tính toán vị trí trung tâm của ô
                        Vector3 treePosition = new Vector3(x * cellSize + cellSize * 0.5f, landY, z * cellSize + cellSize * 0.5f);

                        // Chọn prefab cây ngẫu nhiên
                        GameObject treePrefab = treePrefabs[Random.Range(0, treePrefabs.Length)];

                        // Khởi tạo cây
                        GameObject treeInstance = Instantiate(treePrefab, treePosition, Quaternion.identity, treeContainer.transform);

                        // Ngẫu nhiên hóa rotation và scale
                        treeInstance.transform.eulerAngles = new Vector3(treeInstance.transform.eulerAngles.x, Random.Range(0f, 360f), treeInstance.transform.eulerAngles.z);
                        float scale = Random.Range(0.8f, 1.2f);
                        treeInstance.transform.localScale = Vector3.one * scale;
                    }
                }
            }
        }
    }

    private void DrawEdgeMesh()
    {
        GameObject edgeObject = new GameObject("Edge Mesh");
        edgeObject.transform.parent = transform;
        edgeObject.transform.localPosition = Vector3.zero;

        MeshFilter meshFilter = edgeObject.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = edgeObject.AddComponent<MeshRenderer>();
        meshRenderer.material = edgeMaterial;

        Mesh edgeMesh = new Mesh();
        meshFilter.mesh = edgeMesh;

        var vertices = new List<Vector3>();
        var triangles = new List<int>();
        float finalSeaHeight = seaHeightValue * heightMultiplier; // Chân vách đá sẽ ở độ cao của biển

        for (int x = 0; x < width; x++)
        {
            for (int z = 0; z < height; z++)
            {
                if (GetTerrainTypeAt(x, z) == "Sea") continue;

                TerrainGridObject currentCell = gridSystem.GetGridObject(new GridPosition(x, z)) as TerrainGridObject;
                float landHeight = currentCell.height * heightMultiplier;

                // Check West (x - 1)
                if (GetTerrainTypeAt(x - 1, z) == "Sea")
                {
                    int baseIndex = vertices.Count;
                    vertices.Add(new Vector3(x * cellSize, landHeight, z * cellSize));
                    vertices.Add(new Vector3(x * cellSize, landHeight, (z + 1) * cellSize));
                    vertices.Add(new Vector3(x * cellSize, finalSeaHeight, z * cellSize));
                    vertices.Add(new Vector3(x * cellSize, finalSeaHeight, (z + 1) * cellSize));
                    triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 0);
                    triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);
                }

                // Check East (x + 1)
                if (GetTerrainTypeAt(x + 1, z) == "Sea")
                {
                    int baseIndex = vertices.Count;
                    vertices.Add(new Vector3((x + 1) * cellSize, landHeight, z * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, landHeight, (z + 1) * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, finalSeaHeight, z * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, finalSeaHeight, (z + 1) * cellSize));
                    triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 3);
                    triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 0);
                }

                // Check South (z - 1)
                if (GetTerrainTypeAt(x, z - 1) == "Sea")
                {
                    int baseIndex = vertices.Count;
                    vertices.Add(new Vector3(x * cellSize, landHeight, z * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, landHeight, z * cellSize));
                    vertices.Add(new Vector3(x * cellSize, finalSeaHeight, z * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, finalSeaHeight, z * cellSize));
                    triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 3);
                    triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 0);
                }

                // Check North (z + 1)
                if (GetTerrainTypeAt(x, z + 1) == "Sea")
                {
                    int baseIndex = vertices.Count;
                    vertices.Add(new Vector3(x * cellSize, landHeight, (z + 1) * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, landHeight, (z + 1) * cellSize));
                    vertices.Add(new Vector3(x * cellSize, finalSeaHeight, (z + 1) * cellSize));
                    vertices.Add(new Vector3((x + 1) * cellSize, finalSeaHeight, (z + 1) * cellSize));
                    triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 0);
                    triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);
                }
            }
        }
        edgeMesh.vertices = vertices.ToArray();
        edgeMesh.triangles = triangles.ToArray();
        edgeMesh.RecalculateNormals();
    }

    private string GetTerrainTypeAt(int x, int z)
    {
        if (x < 0 || x >= width || z < 0 || z >= height)
        {
            return "Sea"; // Coi các ô ngoài biên là biển
        }
        var gridObject = gridSystem.GetGridObject(new GridPosition(x, z)) as TerrainGridObject;
        return gridObject?.terrainType ?? "Sea";
    }

    private void DrawTexture()
    {
        Texture2D texture = GenerateTexture();
        MeshRenderer meshRenderer = GetComponent<MeshRenderer>();
        meshRenderer.material = terrainMaterial;
        meshRenderer.material.mainTexture = texture;
    }

    private Texture2D GenerateTexture()
    {
        Texture2D texture = new Texture2D(width, height);
        Color[] colorMap = new Color[width * height];

        for (int x = 0; x < width; x++)
        {
            for (int z = 0; z < height; z++)
            {
                var gridObject = gridSystem.GetGridObject(new GridPosition(x, z));
                var terrainObject = gridObject as TerrainGridObject;
                if (terrainObject != null)
                {
                    colorMap[z * width + x] = terrainObject.terrainType == "Land" ? Color.green : Color.blue;
                }
            }
        }

        texture.filterMode = FilterMode.Point;
        texture.wrapMode = TextureWrapMode.Clamp;
        texture.SetPixels(colorMap);
        texture.Apply();
        return texture;
    }

    private void GenerateMesh()
    {
        mesh.Clear();

        var vertices = new List<Vector3>();
        var triangles = new List<int>();
        var uvs = new List<Vector2>(); // Thêm UVs để map texture

        for (int x = 0; x < width; x++)
        {
            for (int z = 0; z < height; z++)
            {
                var gridObject = gridSystem.GetGridObject(new GridPosition(x, z));
                var terrainObject = gridObject as TerrainGridObject;
                if (terrainObject == null) continue;

                float y = terrainObject.height * heightMultiplier;
                int baseIndex = vertices.Count;

                // Thêm 4 đỉnh cho mỗi ô
                vertices.Add(new Vector3(x * cellSize, y, z * cellSize));
                vertices.Add(new Vector3(x * cellSize, y, (z + 1) * cellSize));
                vertices.Add(new Vector3((x + 1) * cellSize, y, z * cellSize));
                vertices.Add(new Vector3((x + 1) * cellSize, y, (z + 1) * cellSize));

                // Thêm UVs tương ứng với các đỉnh
                uvs.Add(new Vector2((float)x / width, (float)z / height));
                uvs.Add(new Vector2((float)x / width, (float)(z + 1) / height));
                uvs.Add(new Vector2((float)(x + 1) / width, (float)z / height));
                uvs.Add(new Vector2((float)(x + 1) / width, (float)(z + 1) / height));

                // Thêm 2 tam giác để tạo thành một ô vuông
                triangles.Add(baseIndex);
                triangles.Add(baseIndex + 1);
                triangles.Add(baseIndex + 2);
                triangles.Add(baseIndex + 2);
                triangles.Add(baseIndex + 1);
                triangles.Add(baseIndex + 3);
            }
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.uv = uvs.ToArray(); // Gán UVs cho mesh
        mesh.RecalculateNormals();
    }

    /*
    private void OnDrawGizmos()
    {
        if (gridSystem == null) return;

        for (int x = 0; x < gridSystem.GetWidth(); x++)
        {
            for (int z = 0; z < gridSystem.GetHeight(); z++)
            {
                GridPosition gridPosition = new GridPosition(x, z);
                Vector3 worldPosition = gridSystem.GetWorldPosition(gridPosition);

                // Hiển thị thông tin GridObject
                BaseGridObject gridObject = gridSystem.GetGridObject(gridPosition);
                if (gridObject != null)
                {
                    var terrainObject = gridObject as TerrainGridObject;
                    if (terrainObject != null)
                    {
                        Gizmos.color = terrainObject.terrainType == "Land" ? Color.green : Color.blue;
                        Gizmos.DrawCube(worldPosition + new Vector3(cellSize, 0, cellSize) * 0.5f, new Vector3(cellSize, 0.1f, cellSize));
                    }
                }
            }
        }
    }
    */

    public void SetGridObject(GridPosition gridPosition, BaseGridObject gridObject)
    {
        gridSystem.SetGridObject(gridPosition, gridObject);
    }

    public BaseGridObject GetGridObject(GridPosition gridPosition)
    {
        return gridSystem.GetGridObject(gridPosition);
    }
}