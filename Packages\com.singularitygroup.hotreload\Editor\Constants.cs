﻿
namespace SingularityGroup.HotReload.Editor {
    internal static class Constants {
        public const string WebsiteURL = "https://hotreload.net";

        public const string ProductPurchaseURL = WebsiteURL + "/pricing";
        public const string ProductPurchaseBusinessURL = ProductPurchaseURL + "?tab=business";
        public const string DocumentationURL = WebsiteURL + "/documentation";
        public const string AdditionalContentURL = DocumentationURL + "/getting-started#downloading-additional-content";
        public const string DownloadUrl = WebsiteURL + "/download";
        public const string ContactURL = WebsiteURL + "/contact";
        public const string ForumURL = "https://forum.unity.com/threads/hot-reload-edit-code-without-compiling.1389969/";
        public const string ManageLicenseURL = "https://billing.stripe.com/p/login/28odTObUQ0CU0Za3cc";
        public const string ManageAccountURL = "https://users.licensespring.com/login";
        public const string ForgotPasswordURL = "https://users.licensespring.com/reset-password";
        public const string ReportIssueURL = "https://gitlab.com/singularitygroup/********************/-/issues/new";
        public const string TroubleshootingURL = "https://hotreload.net/documentation/troubleshooting";
        public const string RecompileTroubleshootingURL = TroubleshootingURL + "#unity-recompiles-every-time-i-enterexit-playmode";
        public const string FeaturesDocumentationURL = DocumentationURL + "/features";
        public const string MultipleEditorsURL = DocumentationURL + "/multiple-editors";
        public const string DebuggerURL = DocumentationURL + "/debugger";
        public const string UndetectedChangesURL = DocumentationURL + "/getting-started#undetected-changes";
        public const string VoteForAwardURL = "https://awards.unity.com/#best-development-tool";
        public const string UnityStoreRateAppURL = "https://assetstore.unity.com/packages/slug/254358#reviews";
        public const string ChangelogURL = WebsiteURL + "/changelog";
        public const string DiscordInviteUrl = "https://discord.com/invite/kgxAS4Bqxr";
        
        public const int DaysToRateApp = 5;
        public const int RecompileButtonTextHideWidth = 460;
        public const int IndicationTextHideWidth = 360;
        public const int StartButtonTextHideWidth = 400;
        public const int EventsListHideHeight = 360;
        public const int EventsListHideWidth = 425;
        public const int UpgradeLicenseNoteHideWidth = 325;
        public const int UpgradeLicenseNoteHideHeight = 150;
        public const int RateAppHideHeight = 325;
        public const int RateAppHideWidth = 300;
        public const int EventFiltersShownHideWidth = 275;
        public const int ConsumptionsHideWidth = 300;
        public const int ConsumptionsHideHeight = 360;
        
        public const string Only40EntriesShown = "Only last 40 entries are shown";
    }
}