{"asset": "2e24f92ddbfd4704faa2b7ce38941412", "fileName": "01986ed3-00e4-7b7a-ad2d-a99c32554791.png", "prompt": "Create a 2D pixel art isometric sprite sheet for a medieval knight character. The character wears silver armor with a blue cape, holding a sword and shield. The sprite sheet should include animations for idle, walking, and attacking. Each animation should have 4 frames, and the resolution for each frame is 64x64 pixels. The character should face slightly to the right in an isometric perspective. The style is vibrant and detailed. idle\n", "negativePrompt": "", "model": "b91fcf71-f4cf-43cb-ba1e-1f22a1296e46", "modelName": "8-bit Pixel", "customSeed": -1, "w3CTraceId": "c1118279191333df32ba64a7a26c29ee", "refinementMode": "Generation", "pixelateTargetSize": 0, "pixelateKeepImageSize": false, "pixelatePixelBlockSize": 0, "pixelateMode": 0, "pixelateOutlineThickness": 0, "doodles": {"m_Items": []}, "upscaleFactor": 0}